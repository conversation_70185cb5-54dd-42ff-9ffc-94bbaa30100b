package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.TaskFavorite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务收藏Repository
 */
@Mapper
public interface TaskFavoriteRepository extends BaseMapper<TaskFavorite> {

    /**
     * 分页查询收藏夹中的任务
     * @param page 分页参数
     * @param folderId 收藏夹ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 任务分页结果
     */
    @Select("SELECT t.* " +
            "FROM lalp_delivery_task t " +
            "INNER JOIN lalp_task_favorite f ON t.id = f.task_id " +
            "WHERE f.folder_id = #{folderId} " +
            "AND f.creator_id = #{creatorId} " +
            "AND f.tenant_id = #{tenantId} " +
            "AND f.deleted = 0 " +
            "AND t.deleted = 0 " +
            "ORDER BY f.create_time DESC")
    Page<DeliveryTask> selectTasksByFolder(Page<DeliveryTask> page,
                                         @Param("folderId") Long folderId,
                                         @Param("creatorId") Long creatorId,
                                         @Param("tenantId") Long tenantId);

    /**
     * 检查任务是否已收藏到指定收藏夹
     * @param folderId 收藏夹ID
     * @param taskId 任务ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 收藏记录数量
     */
    @Select("SELECT COUNT(*) " +
            "FROM lalp_task_favorite " +
            "WHERE folder_id = #{folderId} " +
            "AND task_id = #{taskId} " +
            "AND creator_id = #{creatorId} " +
            "AND tenant_id = #{tenantId} " +
            "AND deleted = 0")
    Integer countByFolderAndTask(@Param("folderId") Long folderId,
                               @Param("taskId") Long taskId,
                               @Param("creatorId") Long creatorId,
                               @Param("tenantId") Long tenantId);

    /**
     * 查询任务的所有收藏记录
     * @param taskId 任务ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 收藏记录列表
     */
    @Select("SELECT f.*, folder.folder_name " +
            "FROM lalp_task_favorite f " +
            "LEFT JOIN lalp_task_folder folder ON f.folder_id = folder.id " +
            "WHERE f.task_id = #{taskId} " +
            "AND f.creator_id = #{creatorId} " +
            "AND f.tenant_id = #{tenantId} " +
            "AND f.deleted = 0 " +
            "ORDER BY f.create_time DESC")
    List<TaskFavorite> selectByTask(@Param("taskId") Long taskId,
                                  @Param("creatorId") Long creatorId,
                                  @Param("tenantId") Long tenantId);

    /**
     * 批量删除收藏夹中的所有任务
     * @param folderId 收藏夹ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 删除的记录数
     */
    @Select("UPDATE lalp_task_favorite " +
            "SET deleted = 1, update_time = NOW() " +
            "WHERE folder_id = #{folderId} " +
            "AND creator_id = #{creatorId} " +
            "AND tenant_id = #{tenantId} " +
            "AND deleted = 0")
    Integer deleteByFolder(@Param("folderId") Long folderId,
                         @Param("creatorId") Long creatorId,
                         @Param("tenantId") Long tenantId);

    /**
     * 分页查询所有被收藏的任务
     * @param page 分页参数
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 任务分页结果
     */
    @Select("SELECT t.*, MAX(f.create_time) as latest_favorite_time " +
            "FROM lalp_delivery_task t " +
            "INNER JOIN lalp_task_favorite f ON t.id = f.task_id " +
            "WHERE f.creator_id = #{creatorId} " +
            "AND f.tenant_id = #{tenantId} " +
            "AND f.deleted = 0 " +
            "GROUP BY t.id " +
            "ORDER BY latest_favorite_time DESC")
    Page<DeliveryTask> selectAllFavoriteTasks(Page<DeliveryTask> page,
                                            @Param("creatorId") Long creatorId,
                                            @Param("tenantId") Long tenantId);
}
