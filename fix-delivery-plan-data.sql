-- 修复配送任务表中的delivery_plan字段数据
-- 将错误的ARRIVED值改为IMMEDIATE

-- 1. 查看当前有问题的数据
SELECT id, delivery_plan, status 
FROM lalp_delivery_task 
WHERE delivery_plan = 'ARRIVED';

-- 2. 更新错误的delivery_plan值
UPDATE lalp_delivery_task 
SET delivery_plan = 'IMMEDIATE' 
WHERE delivery_plan = 'ARRIVED';

-- 3. 验证修复结果
SELECT id, delivery_plan, status 
FROM lalp_delivery_task 
WHERE id = 124;

-- 4. 同时确保任务状态正确（应该是ARRIVED状态才能标记为DELIVERED）
UPDATE lalp_delivery_task 
SET status = 'ARRIVED' 
WHERE id = 124 AND status = 'PENDING';

-- 5. 查看最终结果
SELECT id, delivery_plan, status, receiver_phone, pickup_code 
FROM lalp_delivery_task 
WHERE id = 124;
