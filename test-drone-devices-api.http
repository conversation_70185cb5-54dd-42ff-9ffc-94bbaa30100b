### 测试无人机设备接口
### 基础URL: http://localhost:8081

### 1. 分页查询无人机设备列表 (基础查询)
GET http://localhost:8081/api/backend/drone-devices
Content-Type: application/json

### 2. 分页查询无人机设备列表 (带分页参数)
GET http://localhost:8081/api/backend/drone-devices?pageNum=1&pageSize=5
Content-Type: application/json

### 3. 分页查询无人机设备列表 (带关键词搜索)
GET http://localhost:8081/api/backend/drone-devices?keyword=无人机
Content-Type: application/json

### 4. 分页查询无人机设备列表 (按状态过滤 - 在线)
GET http://localhost:8081/api/backend/drone-devices?status=1
Content-Type: application/json

### 5. 分页查询无人机设备列表 (按状态过滤 - 离线)
GET http://localhost:8081/api/backend/drone-devices?status=0
Content-Type: application/json

### 6. 分页查询无人机设备列表 (组合查询)
GET http://localhost:8081/api/backend/drone-devices?pageNum=1&pageSize=10&keyword=DR&status=1
Content-Type: application/json

### 7. 获取无人机设备详情
GET http://localhost:8081/api/backend/drone-devices/1
Content-Type: application/json

### 8. 带租户ID的查询
GET http://localhost:8081/api/backend/drone-devices
Content-Type: application/json
X-Tenant-Id: 1

### 9. 使用curl命令测试 (复制到命令行执行)
# curl -X GET "http://localhost:8081/api/backend/drone-devices?pageNum=1&pageSize=5" \
#      -H "Content-Type: application/json"

### 10. 验证新增字段的curl命令
# curl -X GET "http://localhost:8081/api/backend/drone-devices" \
#      -H "Content-Type: application/json" | jq '.data.list[0] | {deviceType, online, lastCommunicationTime, batteryLevel}'
